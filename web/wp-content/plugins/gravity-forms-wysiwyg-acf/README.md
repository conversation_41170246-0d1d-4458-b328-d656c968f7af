# Gravity Forms WYSIWYG ACF Integration

A WordPress plugin that adds a Gravity Forms button to WYSIWYG ACF components, allowing users to easily insert forms into content areas.

## Features

- **Easy Form Insertion**: Adds a Gravity Forms button to the WYSIWYG editor toolbar
- **Form Selection Modal**: Beautiful modal interface to browse and select forms
- **Live Preview**: See a preview of the selected form before inserting
- **Form Information**: Display form title, description, and field count
- **Keyboard Shortcut**: Use Ctrl+Shift+G to quickly open the form selector
- **Responsive Design**: Works on desktop and mobile devices
- **Security**: Proper nonce verification and capability checks

## Requirements

- WordPress 5.0+
- Advanced Custom Fields (ACF) Pro or Free
- Gravity Forms
- PHP 7.0+

## Installation

1. Upload the plugin folder to `/wp-content/plugins/`
2. Activate the plugin through the 'Plugins' menu in WordPress
3. The plugin will automatically add the Gravity Forms button to WYSIWYG editors in ACF fields

## Usage

### Adding Forms to WYSIWYG Content

1. **Open any ACF WYSIWYG field** in the WordPress admin
2. **Click the Gravity Forms button** in the editor toolbar (looks like a form icon)
3. **Select a form** from the modal that appears
4. **Preview the form** to ensure it's the correct one
5. **Click "Insert Form"** to add the shortcode to your content

### Keyboard Shortcut

- Press `Ctrl+Shift+G` (or `Cmd+Shift+G` on Mac) to quickly open the form selector

### Form Display Options

The plugin inserts forms with these default settings:
- `title="false"` - Hides the form title
- `description="false"` - Hides the form description

You can manually edit the shortcode after insertion to customize these options:

```
[gravityform id="1" title="true" description="true"]
```

## Shortcode Parameters

The plugin generates standard Gravity Forms shortcodes. Available parameters include:

- `id` - Form ID (required)
- `title` - Show/hide form title (true/false)
- `description` - Show/hide form description (true/false)
- `ajax` - Enable AJAX submission (true/false)
- `tabindex` - Starting tab index

Example:
```
[gravityform id="1" title="false" description="false" ajax="true"]
```

## Styling

The plugin includes CSS for the modal interface. You can override styles by adding CSS to your theme:

```css
/* Customize modal appearance */
.gf-modal {
    max-width: 1000px;
}

/* Customize form preview */
.gf-preview-form {
    border: 2px solid #your-color;
}
```

## Troubleshooting

### Button Not Appearing

1. Ensure both ACF and Gravity Forms are installed and activated
2. Check that you're editing an ACF WYSIWYG field (not the main WordPress editor)
3. Clear any caching plugins

### Forms Not Loading

1. Verify you have forms created in Gravity Forms
2. Check that your user account has permission to edit posts
3. Look for JavaScript errors in the browser console

### Permission Issues

The plugin requires users to have the `edit_posts` capability to use the form insertion feature.

## Hooks and Filters

### Actions

- `gf_wysiwyg_acf_before_modal` - Fired before the modal is displayed
- `gf_wysiwyg_acf_after_insert` - Fired after a form is inserted

### Filters

- `gf_wysiwyg_acf_form_preview` - Modify the form preview HTML
- `gf_wysiwyg_acf_shortcode` - Modify the generated shortcode
- `gf_wysiwyg_acf_modal_title` - Change the modal title

Example:
```php
// Customize the shortcode output
add_filter('gf_wysiwyg_acf_shortcode', function($shortcode, $form_id) {
    return '[gravityform id="' . $form_id . '" title="true" ajax="true"]';
}, 10, 2);
```

## Development

### File Structure

```
gravity-forms-wysiwyg-acf/
├── gravity-forms-wysiwyg-acf.php  # Main plugin file
├── assets/
│   ├── css/
│   │   └── admin.css              # Modal and UI styles
│   └── js/
│       ├── admin.js               # Main JavaScript functionality
│       └── tinymce-plugin.js      # TinyMCE integration
└── README.md                      # This file
```

### Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Test thoroughly
5. Submit a pull request

## Changelog

### 1.0.0
- Initial release
- Form selection modal
- Live form preview
- TinyMCE integration
- Keyboard shortcuts
- Responsive design

## Support

For support and bug reports, please contact Fat Beehive or create an issue in the project repository.

## License

This plugin is licensed under the GPL v2 or later.
