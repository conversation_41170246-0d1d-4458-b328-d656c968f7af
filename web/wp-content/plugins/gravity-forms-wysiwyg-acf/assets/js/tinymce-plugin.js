/**
 * TinyMCE Plugin for Gravity Forms WYSIWYG ACF Integration
 */

(function() {
    'use strict';

    tinymce.PluginManager.add('gf_form_button', function(editor, url) {
        
        // Add button to toolbar
        editor.addButton('gf_form_button', {
            title: 'Insert Gravity Form',
            icon: 'gf-form-icon',
            onclick: function() {
                // Check if our modal system is available
                if (typeof window.GFWysiwygAcf !== 'undefined') {
                    window.GFWysiwygAcf.openModal(editor);
                } else {
                    // Fallback: simple prompt
                    var formId = prompt('Enter Gravity Form ID:');
                    if (formId && !isNaN(formId)) {
                        var shortcode = '[gravityform id="' + formId + '" title="false" description="false"]';
                        editor.insertContent(shortcode);
                    }
                }
            }
        });

        // Add menu item
        editor.addMenuItem('gf_form_button', {
            text: 'Insert Gravity Form',
            icon: 'gf-form-icon',
            context: 'insert',
            onclick: function() {
                if (typeof window.GFWysiwygAcf !== 'undefined') {
                    window.GFWysiwygAcf.openModal(editor);
                } else {
                    var formId = prompt('Enter Gravity Form ID:');
                    if (formId && !isNaN(formId)) {
                        var shortcode = '[gravityform id="' + formId + '" title="false" description="false"]';
                        editor.insertContent(shortcode);
                    }
                }
            }
        });

        // Optional: Add shortcut key (Ctrl+Shift+G)
        editor.addShortcut('ctrl+shift+g', 'Insert Gravity Form', function() {
            if (typeof window.GFWysiwygAcf !== 'undefined') {
                window.GFWysiwygAcf.openModal(editor);
            }
        });

        return {
            getMetadata: function() {
                return {
                    name: 'Gravity Forms Button',
                    url: 'https://fatbeehive.com'
                };
            }
        };
    });

})();
